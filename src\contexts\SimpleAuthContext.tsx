import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { createClient } from '@supabase/supabase-js';

// Create a single supabase client for the entire app
const supabaseUrl = 'https://mixjfinrxzpplzqidlas.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1peGpmaW5yeHpwcGx6cWlkbGFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODY0MzgsImV4cCI6MjA1OTE2MjQzOH0.g4lTxBEZbG_GJKy_WrBJwW0H2tr9XaZHLzXpjdtbCzA';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

interface AuthContextType {
  isAuthenticated: boolean;
  loading: boolean;
  user: any;
  login: (email: string, password: string) => Promise<{ error?: any }>;
  logout: () => Promise<void>;
  signup: (email: string, password: string, userData: any) => Promise<{ error?: any, user?: any }>;
  resetPassword: (email: string) => Promise<{ error?: any, data?: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Check for existing session on mount
  useEffect(() => {
    const checkSession = async () => {
      try {
        console.log('Checking for existing session...');
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error checking session:', error);
          setIsAuthenticated(false);
          setUser(null);
        } else if (data.session) {
          console.log('Session found:', data.session.user.id);
          setIsAuthenticated(true);
          setUser(data.session.user);
        } else {
          console.log('No session found');
          setIsAuthenticated(false);
          setUser(null);
        }
      } catch (error) {
        console.error('Error in checkSession:', error);
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    // Add a safety timeout
    const timeout = setTimeout(() => {
      if (loading) {
        console.log('Loading timeout reached, forcing to false');
        setLoading(false);
      }
    }, 3000);

    checkSession();

    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event);

      if (session) {
        console.log('User authenticated:', session.user.id);
        setIsAuthenticated(true);
        setUser(session.user);
      } else {
        console.log('User signed out');
        setIsAuthenticated(false);
        setUser(null);
      }

      setLoading(false);
    });

    return () => {
      clearTimeout(timeout);
      subscription.unsubscribe();
    };
  }, [loading]);

  // Login function
  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      console.log('Logging in with email:', email);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Login error:', error);
        setLoading(false);

        // Map common Supabase auth errors to user-friendly messages
        let userFriendlyError = error;

        if (error.message.includes('Invalid login credentials')) {
          userFriendlyError = { ...error, message: 'Invalid email or password. Please check your credentials and try again.' };
        } else if (error.message.includes('Email not confirmed')) {
          userFriendlyError = { ...error, message: 'Please check your email to confirm your account before signing in.' };
        } else if (error.message.includes('Too many requests')) {
          userFriendlyError = { ...error, message: 'Too many login attempts. Please wait a few minutes before trying again.' };
        } else if (error.message.includes('User not found')) {
          userFriendlyError = { ...error, message: 'No account found with this email address. Please check your email or create a new account.' };
        } else if (error.message.includes('Email rate limit exceeded')) {
          userFriendlyError = { ...error, message: 'Too many login attempts. Please wait a few minutes before trying again.' };
        }

        return { error: userFriendlyError };
      }

      console.log('Login successful:', data.user?.id);

      // Don't force navigation here - let the component handle it
      // This prevents page refresh that would clear error messages
      return { error: null };
    } catch (error) {
      console.error('Login error:', error);
      setLoading(false);
      return { error };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setLoading(true);
      await supabase.auth.signOut();
      setIsAuthenticated(false);
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Signup function
  const signup = async (email: string, password: string, userData: any) => {
    try {
      setLoading(true);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      });

      if (error) {
        console.error('Signup error:', error);
        setLoading(false);

        // Map common Supabase auth errors to user-friendly messages
        let userFriendlyError = error;

        if (error.message.includes('User already registered')) {
          userFriendlyError = { ...error, message: 'An account with this email already exists. Please sign in instead.' };
        } else if (error.message.includes('Password should be at least')) {
          userFriendlyError = { ...error, message: 'Password must be at least 6 characters long.' };
        } else if (error.message.includes('Invalid email') || error.message.includes('is invalid')) {
          userFriendlyError = { ...error, message: 'Please enter a valid email address.' };
        } else if (error.message.includes('Signup is disabled')) {
          userFriendlyError = { ...error, message: 'Account creation is currently disabled. Please contact support.' };
        } else if (error.message.includes('Password is too weak')) {
          userFriendlyError = { ...error, message: 'Password is too weak. Please choose a stronger password.' };
        } else if (error.message.includes('Email address')) {
          userFriendlyError = { ...error, message: 'Please enter a valid email address.' };
        }

        return { error: userFriendlyError };
      }

      // Create user record in the database
      if (data.user) {
        try {
          await supabase.from('users').insert([
            {
              user_id: data.user.id,
              email: data.user.email,
              first_name: userData.first_name,
              last_name: userData.last_name,
              role: 'owner',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
          ]);
        } catch (dbError) {
          console.error('Error creating user record:', dbError);
        }
      }

      setLoading(false);
      return { error: null, user: data.user };
    } catch (error) {
      console.error('Signup error:', error);
      setLoading(false);
      return { error };
    }
  };

  // Reset password function
  const resetPassword = async (email: string) => {
    try {
      setLoading(true);
      console.log('Sending password reset email to:', email);

      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        console.error('Reset password error:', error);
        setLoading(false);
        return { error };
      }

      console.log('Password reset email sent successfully');
      setLoading(false);
      return { data, error: null };
    } catch (error) {
      console.error('Reset password error:', error);
      setLoading(false);
      return { error };
    }
  };

  const value = {
    isAuthenticated,
    loading,
    user,
    login,
    logout,
    signup,
    resetPassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
