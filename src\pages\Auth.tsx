import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Scissors, Mail, Lock, User, Building2, MapPin, ArrowRight, CheckCircle2, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { supabase, useAuth } from '../contexts/SimpleAuthContext';

export function Auth() {
  const [isLogin, setIsLogin] = useState(true);
  const [showSuccess, setShowSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [resetEmailSent, setResetEmailSent] = useState(false);

  // Form inputs
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [salonName, setSalonName] = useState('');
  const [address, setAddress] = useState('');
  const [resetEmail, setResetEmail] = useState('');

  const navigate = useNavigate();
  const { login, signup, resetPassword } = useAuth();

  const [emailConfirmationSent, setEmailConfirmationSent] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleForgotPassword = (e: React.MouseEvent) => {
    e.preventDefault();
    setError(null);
    setShowForgotPassword(true);
    // Initialize reset email with current email if available
    if (email) {
      setResetEmail(email);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!resetEmail) {
      setError('Please enter your email address');
      return;
    }

    setLoading(true);

    try {
      const { error } = await resetPassword(resetEmail);

      if (error) {
        throw error;
      }

      setResetEmailSent(true);
    } catch (error: any) {
      console.error('Password reset error:', error);
      setError(error.message || 'Failed to send password reset email. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToLogin = () => {
    setShowForgotPassword(false);
    setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    console.log('🚀 Form submission started');
    e.preventDefault();
    console.log('✅ preventDefault() called successfully');
    setError(null);
    setEmailConfirmationSent(false);

    // Client-side validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      console.log('❌ Email validation failed');
      setError('Please enter a valid email address.');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters long.');
      return;
    }

    if (!isLogin) {
      if (!firstName.trim()) {
        setError('First name is required.');
        return;
      }
      if (!lastName.trim()) {
        setError('Last name is required.');
        return;
      }
      if (!salonName.trim()) {
        setError('Salon name is required.');
        return;
      }
    }

    setLoading(true);

    try {
      if (isLogin) {
        console.log('🔐 Starting login process...');
        // Sign in with existing account
        const { error } = await login(email, password);
        console.log('🔐 Login function returned:', { error });

        if (error) {
          console.log('❌ Login error occurred:', error);
          // Handle the "Email not confirmed" error specially to show the confirmation message
          if (error.message && error.message.includes('confirm your account')) {
            setEmailConfirmationSent(true);
          }

          // The error mapping is now handled in SimpleAuthContext, so we can directly throw the error
          throw new Error(error.message || 'Login failed. Please try again.');
        }

        // Login successful - navigate to dashboard
        console.log('✅ Login successful in Auth.tsx - about to navigate');
        navigate('/dashboard');
        console.log('✅ Navigation called');
      } else {
        // Create new account
        const userData = {
          first_name: firstName,
          last_name: lastName,
          salon_name: salonName,
          salon_address: address,
        };

        const { error, user } = await signup(email, password, userData);

        if (error) {
          // The error mapping is now handled in SimpleAuthContext, so we can directly throw the error
          throw new Error(error.message || 'Account creation failed. Please try again.');
        }

        if (user) {
          // Check if user needs email confirmation
          if (user.email_confirmed_at === null) {
            // User needs to confirm email
            setEmailConfirmationSent(true);
            setShowSuccess(true);
          } else {
            // User is confirmed, proceed with setup
            try {
              // Create a salon record
              const { data: salonData, error: salonError } = await supabase
                .from('salons')
                .insert([
                  {
                    owner_user_id: user.id,
                    name: salonName,
                    address: address || null,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                  },
                ])
                .select();

              if (!salonError && salonData && salonData.length > 0) {
                const salonId = salonData[0].salon_id;

                // Create default services
                const defaultServices = [
                  { name: 'Haircut & Style', description: 'Basic haircut and styling', price: 60, duration: 45 },
                  { name: 'Color Treatment', description: 'Hair coloring service', price: 120, duration: 90 },
                  { name: 'Manicure', description: 'Basic manicure service', price: 40, duration: 30 },
                  { name: 'Pedicure', description: 'Basic pedicure service', price: 50, duration: 45 },
                  { name: 'Facial', description: 'Basic facial treatment', price: 80, duration: 60 },
                ];

                const servicesWithSalonId = defaultServices.map(service => ({
                  ...service,
                  salon_id: salonId,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                }));

                await supabase.from('services').insert(servicesWithSalonId);

                // Update user with salon ID
                await supabase
                  .from('users')
                  .update({ salon_id: salonId })
                  .eq('user_id', user.id);
              }
            } catch (setupError) {
              console.error('Error in setup process:', setupError);
            }

            // Show success message
            setShowSuccess(true);
          }
        } else {
          // No user returned - likely email confirmation required
          setEmailConfirmationSent(true);
          setShowSuccess(true);
        }
      }
    } catch (error) {
      console.error('Authentication error:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleSuccessContinue = () => {
    if (emailConfirmationSent) {
      // If email confirmation is required, go back to login
      setShowSuccess(false);
      setIsLogin(true);
    } else {
      // Otherwise, go to dashboard
      navigate('/dashboard');
    }
  };

  // Success screen after account creation
  if (showSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 text-center space-y-6">
            <div className="flex justify-center">
              <CheckCircle2 className="h-16 w-16 text-green-500" />
            </div>

            {emailConfirmationSent ? (
              <>
                <h2 className="text-2xl font-bold text-gray-900">Check Your Email!</h2>
                <p className="text-gray-600">Your salon account has been created successfully.</p>

                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-lg border-2 border-yellow-200 text-left">
                  <div className="flex items-center mb-3">
                    <Mail className="h-6 w-6 text-yellow-600 mr-2" />
                    <h3 className="text-lg font-semibold text-yellow-800">Email Confirmation Required</h3>
                  </div>
                  <p className="text-sm text-yellow-700 mb-3">
                    We've sent a confirmation link to:
                  </p>
                  <p className="text-base font-medium text-yellow-800 bg-yellow-100 px-3 py-2 rounded mb-3">
                    {email}
                  </p>
                  <p className="text-sm text-yellow-700">
                    Please check your inbox (and spam folder) and click the confirmation link to activate your account before signing in.
                  </p>
                </div>
              </>
            ) : (
              <>
                <h2 className="text-2xl font-bold text-gray-900">Welcome aboard!</h2>
                <p className="text-gray-600">Your salon account has been created successfully.</p>
              </>
            )}

            {!emailConfirmationSent && (
              <div className="bg-blue-50 p-4 rounded-md text-left">
                <h3 className="text-md font-medium text-blue-800 mb-1">What's been set up for you:</h3>
                <ul className="text-sm text-blue-700 list-disc pl-5 space-y-1">
                  <li>Your salon: <span className="font-medium">{salonName}</span></li>
                  <li>Default services catalog with common salon services</li>
                  <li>Basic appointment management system</li>
                </ul>
              </div>
            )}

            <button
              onClick={handleSuccessContinue}
              className="w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {emailConfirmationSent ? 'Go to Login Page' : 'Continue to Dashboard'}
              <ArrowRight className="ml-2 h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Forgot password screen
  if (showForgotPassword) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="flex justify-center">
            <div className="h-12 w-12 bg-indigo-600 rounded-lg flex items-center justify-center">
              <Scissors className="h-8 w-8 text-white" />
            </div>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Reset your password
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Enter your email address and we'll send you a link to reset your password
          </p>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
            {error && (
              <div className="rounded-md bg-red-50 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-5 w-5 text-red-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">{error}</h3>
                  </div>
                </div>
              </div>
            )}

            {resetEmailSent ? (
              <div className="rounded-md bg-green-50 p-4 border border-green-200">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <CheckCircle2 className="h-5 w-5 text-green-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">Password reset email sent</h3>
                    <p className="text-sm text-green-700 mt-1">
                      Please check your email for instructions to reset your password.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <form className="space-y-6" onSubmit={handleResetPassword}>
                <div>
                  <label htmlFor="resetEmail" className="block text-sm font-medium text-gray-700">
                    Email address
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="resetEmail"
                      name="resetEmail"
                      type="email"
                      autoComplete="email"
                      required
                      value={resetEmail}
                      onChange={(e) => setResetEmail(e.target.value)}
                      className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'Sending...' : 'Send Reset Link'}
                  </button>

                  <button
                    type="button"
                    onClick={handleBackToLogin}
                    className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Back to Login
                  </button>
                </div>
              </form>
            )}

            {resetEmailSent && (
              <div className="mt-6">
                <button
                  type="button"
                  onClick={handleBackToLogin}
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Back to Login
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="h-12 w-12 bg-indigo-600 rounded-lg flex items-center justify-center">
            <Scissors className="h-8 w-8 text-white" />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {isLogin ? 'Welcome back' : 'Create your salon account'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {isLogin ? (
            'Sign in to manage your salon'
          ) : (
            'Get started with your salon management'
          )}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    if (error) setError(null);
                  }}
                  className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            {/* Error message display between email and password fields for login */}
            {error && isLogin && (
              <div className="rounded-md bg-red-50 p-4 border-2 border-red-200">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-6 w-6 text-red-500" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-base font-semibold text-red-800 mb-1">Login Error</h3>
                    <p className="text-sm text-red-700">{error}</p>
                    {error.includes('confirm your account') && (
                      <div className="mt-3 p-3 bg-red-100 rounded-md">
                        <p className="text-sm text-red-800 font-medium">
                          📧 Check your email inbox for a confirmation link
                        </p>
                        <p className="text-xs text-red-600 mt-1">
                          If you can't find it, check your spam folder or try signing up again.
                        </p>
                      </div>
                    )}
                    {error.includes('Invalid login credentials') && (
                      <div className="mt-3 p-3 bg-red-100 rounded-md">
                        <p className="text-sm text-red-800 font-medium">
                          💡 Please check your email and password and try again
                        </p>
                      </div>
                    )}
                    {error.includes('valid email') && (
                      <div className="mt-3 p-3 bg-red-100 rounded-md">
                        <p className="text-sm text-red-800 font-medium">
                          ✉️ Please check your email format (example: <EMAIL>)
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete={isLogin ? "current-password" : "new-password"}
                  required
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value);
                    if (error) setError(null);
                  }}
                  className="appearance-none block w-full pl-10 pr-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="••••••••"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {!isLogin && (
              <>
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                      First name
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        name="firstName"
                        id="firstName"
                        autoComplete="given-name"
                        required
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                      Last name
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        name="lastName"
                        id="lastName"
                        autoComplete="family-name"
                        required
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label htmlFor="salonName" className="block text-sm font-medium text-gray-700">
                    Salon name
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Building2 className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      name="salonName"
                      id="salonName"
                      required
                      value={salonName}
                      onChange={(e) => setSalonName(e.target.value)}
                      className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="Your Salon Name"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                    Salon address (optional)
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <MapPin className="h-5 w-5 text-gray-400" />
                    </div>
                    <textarea
                      id="address"
                      name="address"
                      rows={3}
                      value={address}
                      onChange={(e) => setAddress(e.target.value)}
                      className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="Enter your salon's address"
                    />
                  </div>
                </div>
              </>
            )}

            {isLogin && (
              <div className="flex items-center justify-end">
                <div className="text-sm">
                  <a
                    href="#"
                    onClick={handleForgotPassword}
                    className="font-medium text-indigo-600 hover:text-indigo-500"
                  >
                    Forgot your password?
                  </a>
                </div>
              </div>
            )}

            {/* Error message display for signup errors */}
            {error && !isLogin && (
              <div className="rounded-md bg-red-50 p-4 border-2 border-red-200 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-6 w-6 text-red-500" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-base font-semibold text-red-800 mb-1">Signup Error</h3>
                    <p className="text-sm text-red-700">{error}</p>
                    {error.includes('already exists') && (
                      <div className="mt-3 p-3 bg-red-100 rounded-md">
                        <p className="text-sm text-red-800 font-medium">
                          💡 Try signing in instead of creating a new account
                        </p>
                      </div>
                    )}
                    {error.includes('valid email') && (
                      <div className="mt-3 p-3 bg-red-100 rounded-md">
                        <p className="text-sm text-red-800 font-medium">
                          ✉️ Please check your email format (example: <EMAIL>)
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {emailConfirmationSent && isLogin && (
              <div className="rounded-md bg-yellow-50 p-4 border border-yellow-200">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">Email confirmation required</h3>
                    <p className="text-sm text-yellow-700 mt-1">
                      Please check your email and click the confirmation link before signing in.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Processing...' : (isLogin ? 'Sign in' : 'Create account')}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  {isLogin ? "Don't have an account?" : "Already have an account?"}
                </span>
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={() => {
                  if (isLogin) {
                    // Navigate to signup page
                    navigate('/signup');
                  } else {
                    // Toggle back to login mode
                    setIsLogin(true);
                    setError(null);
                    setEmailConfirmationSent(false);
                  }
                }}
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                {isLogin ? 'Create a new account' : 'Sign in to existing account'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}